package com.mzj.py.mservice.device.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.mzj.py.aop.StorePermission;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.StringUtils;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.compound.utils.FileRenameUtils;
import com.mzj.py.mservice.compound.utils.FileUtil;
import com.mzj.py.mservice.device.vo.*;
import com.mzj.py.mservice.home.entity.*;
import com.mzj.py.mservice.home.repository.*;
import com.mzj.py.mservice.oss.service.OSSService;
import com.mzj.py.mservice.redis.RedisService;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.mzj.py.aop.DevicePermission;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Predicate;
import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: duanjinze
 * @date: 2022/11/15 16:03
 * @version: 1.0
 */
@Slf4j
@Service
public class DeviceService {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private DeviceRepository deviceRepository;
    @Autowired
    private DeviceVoiceRepository deviceVoiceRepository;
    @Autowired
    private OSSService ossService;
    @Autowired
    private StoreService shopService;
    @Autowired
    private ShopRepository shopRepository;
    @Autowired
    private ShopUserRefRepository shopUserRefRepository;
    @Resource
    private RemoteDeviceService remoteDeviceService;

    @Autowired
    private VoicePacketRepository voicePacketRepository;

    @Resource
    private VoiceWorkRepository workRepository;

    @Value("${ali.cdn.bucket.url}")
    private String cdnUrl;

    @Value("${ali.oss.downFile.tempdir}")
    private String appUrl;
    @Value("${ali.oss.bucket.url}")
    private String ossUrl;

    @Autowired
    private RedisService redisService;

    /**
     * 我的设备列表接口
     *
     * @return
     */
    public ResultBean<Map<String, Object>> list(DeviceQueryVo queryVo) {
        int pageNumber = queryVo.getPageNumber() > 0 ? queryVo.getPageNumber() - 1 : 0;
        Pageable pageable = PageRequest.of(pageNumber, queryVo.getPageSize(),
                Sort.by(Sort.Direction.DESC, "createTime"));
        Page<Device> page = deviceRepository.findAll((root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            // 动态添加模糊查询条件（参数非空时生效）
            if (StringUtils.isNotBlank(queryVo.getKeyword())) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + queryVo.getKeyword() + "%"));
            }
            if (queryVo.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), queryVo.getStatus()));
            }
            if (queryVo.getBindStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("bindStatus"), queryVo.getBindStatus()));
            }
            predicates.add(criteriaBuilder.or(root.get("shopId").in(queryVo.getShopIds()),
                    criteriaBuilder.equal(root.get("createId"), queryVo.getCreateId())));
            predicates.add(criteriaBuilder.equal(root.get("delStatus"), 0));
            // 组合所有条件（AND 关系）
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        }, pageable);
        if (page == null) {
            return ResultBean.getResultMap(0, new ArrayList<>());
        }
        return ResultBean.getResultMap((int) page.getTotalElements(), page.getContent());
    }

    /**
     * 解绑
     *
     * @return
     */
    @DevicePermission(key = "deviceId")
    public ResultBean<Boolean> unBind(Long deviceId) {
        Optional<Device> byId = deviceRepository.findById(deviceId);
        if (!byId.isPresent()) {
            return ResultBean.failedResultWithMsg("设备不存在");
        }
        Device device = byId.get();
        device.setUserId(null);
        device.setShopId(null);
        device.setBindStatus(0);
        deviceRepository.save(device);
        return ResultBean.successfulResult(true);
    }

    /**
     * 绑定
     *
     * @return
     */
    @StorePermission(key = "shopId")
    public ResultBean<Boolean> bind(DeviceUnBindVo bindVo) {
        if (!deviceRepository.existsById(bindVo.getId())) {
            return ResultBean.failedResultWithMsg("设备不存在");
        }
        if (!shopRepository.existsById(bindVo.getShopId())) {
            return ResultBean.failedResultWithMsg("门店不存在");
        }
        ShopUserRef shopRef = shopUserRefRepository.findByShopIdAndRole(bindVo.getShopId(),
                StoreUserTypeEnum.ADMIN.getCode());
        if (shopRef == null) {
            return ResultBean.failedResultWithMsg("门店未绑定管理员");
        }
        Device device = deviceRepository.findById(bindVo.getId())
                .orElseThrow(() -> new ServiceException("设备不存在"));
        if (device.getShopId() != null) {
            return ResultBean.failedResultWithMsg("设备已绑定门店,请联系客服解绑");
        }
        device.setUserId(shopRef.getUserId());
        device.setShopId(bindVo.getShopId());
        device.setBindStatus(1);
        deviceRepository.save(device);
        return ResultBean.successfulResult(true);
    }

    /**
     * 新增 与 修改
     *
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultBean<Boolean> addOrUpdate(Device vo) {
        // 新增
        if (vo.getShopId() == null) {
            ShopUserRef ref = shopService.createShop(vo.getUserId());
            if (ref == null) {
                throw new ServiceException("创建门店失败");
            }
            vo.setShopId(ref.getShopId());
            vo.setUserId(ref.getUserId());
        }
        if (vo.getId() == null) {
            if (StringUtils.isNotBlank(vo.getSn())) {
                Device device = deviceRepository.findBySn(vo.getSn());
                if (device != null && device.getShopId() != null) {
                    return ResultBean.failedResultWithMsg("设备已绑定,请联系客服解绑");
                }
            }
            vo.setBindStatus(1);
            vo.setCreateId(vo.getUserId());
            vo.setCreateTime(new Date());
            vo.setDelStatus(0);
            vo.setStatus(0);
            vo.setUseStatus(1);
            deviceRepository.save(vo);
        } else {
            // 修改
            if (!deviceRepository.existsById(vo.getId())) {
                throw new ServiceException("无此设备数据");
            }
            Device oldDev = deviceRepository.findById(vo.getId()).get();
            oldDev.setShopId(vo.getShopId());
            oldDev.setName(vo.getName());
            oldDev.setSn(vo.getSn());
            oldDev.setVolume(vo.getVolume());
            deviceRepository.save(oldDev);
        }
        return ResultBean.successfulResult(true);
    }

    /**
     * 设备详情
     *
     * @return
     */
    @DevicePermission(key = "deviceId")
    public ResultBean<DetailVo> detail(Long deviceId, Integer pageSize, Integer pageNumber) {
        if (deviceId == null) {
            return ResultBean.failedResultWithMsg("设备id不能为空");
        }
        DetailVo detailVo = BeanUtil.toBean(
                deviceRepository.findById(deviceId).orElseThrow(() -> new ServiceException("无此设备数据")),
                DetailVo.class);
        StringBuilder where = new StringBuilder();
        List<Object> args = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();
        String sql = "SELECT ddv.*, ch.voice_name AS voiceName, mu.music_url AS musicUrl FROM dub_device_voice ddv\n" +
                "LEFT JOIN dub_anchor ch ON ch.id = ddv.voice_id\n" +
                "LEFT JOIN dub_background_music mu ON mu.id = ddv.background_music_id " +
                "WHERE ddv.device_id = ? and ddv.del_status != 1 " +
                "order by ddv.sortby \n";
        args.add(deviceId);
        Long count = jdbcTemplate.queryForObject("select count(1) from (" + sql + where + ") t", Long.class,
                args.toArray());
        if (count == null || count == 0) {
            map.put("count", 0);
            map.put("result", null);
            detailVo.setMap(map);
            return ResultBean.successfulResult(detailVo);
        }
        where.append(" limit ? offset ? ");
        args.add(pageSize);
        args.add(pageSize * pageNumber);
        List<DeviceVoice> deviceVoices = jdbcTemplate.query(sql + where, new BeanPropertyRowMapper<>(DeviceVoice.class),
                args.toArray());
        map.put("count", count);
        map.put("result", deviceVoices);
        detailVo.setMap(map);
        return ResultBean.successfulResult(detailVo);
    }

    /**
     * 修改设备与语音表
     *
     * @param vo
     * @return
     */
    @DevicePermission(key = "deviceId")
    public ResultBean<Boolean> updateDeviceVoice(DeviceVoiceVo vo) {
        if (vo.getId() == null) {
            return ResultBean.failedResultWithMsg("设备与语音表id不能为空");
        }
        if (vo.getDeviceId() == null) {
            return ResultBean.failedResultWithMsg("设备id不能为空");
        }
        if (vo.getSpeed() == null) {
            return ResultBean.failedResultWithMsg("语速不能为空");
        }
        if (vo.getVolume() == null) {
            return ResultBean.failedResultWithMsg("音量不能为空");
        }
        if (vo.getBackgroundMusicId() != null) {
            if (vo.getBackgroundMusicVolume() == null) {
                return ResultBean.failedResultWithMsg("背景音乐音量不能为空");
            }
        }
        if (StringUtils.isBlank(vo.getVoiceUrl())) {
            return ResultBean.failedResultWithMsg("语音包路径不能为空");
        }
        File file = ossService.getObjectFile(null, vo.getVoiceUrl());
        ossService.deleteObject(null, vo.getVoiceUrl());
        String ossUrl = ossService.putFile(null, file, "voice");
        // 修改
        Optional<DeviceVoice> byId = deviceVoiceRepository.findById(vo.getId());
        if (byId.isPresent()) {
            DeviceVoice deviceVoice = byId.get();
            deviceVoice.setSpeed(vo.getSpeed());
            deviceVoice.setVolume(vo.getVolume());
            deviceVoice.setPitch(vo.getPitch());
            deviceVoice.setBackgroundMusicVolume(vo.getBackgroundMusicVolume());
            deviceVoice.setVoiceUrl(ossUrl);
            deviceVoiceRepository.save(deviceVoice);
        }
        return ResultBean.successfulResult(true);
    }

    @DevicePermission(key = "id")
    public ResultBean<Boolean> deleteDeviceVoice(Long id) {
        if (id == null) {
            return ResultBean.failedResultWithMsg("设备与语音包关系表id不能为空");
        }
        Optional<DeviceVoice> byId = deviceVoiceRepository.findById(id);
        if (byId.isPresent()) {
            deviceVoiceRepository.updateDelStatusById(id);
        }
        return ResultBean.successfulResult(true);
    }

    /**
     * 设备音频列表与设备已绑定列表
     *
     * @param deviceId
     * @param voiceVoList
     * @return
     */
    @DevicePermission(key = "deviceId")
    public ResultBean<DetailVo> deviceVoicedetail(Long deviceId, List<DevVoiceVo> voiceVoList) {
        DetailVo detailVo = BeanUtil.toBean(
                deviceRepository.findById(deviceId).orElseThrow(() -> new ServiceException("无此设备数据")),
                DetailVo.class);

        List<Object> args = new ArrayList<>();
        Map<String, Object> map = new HashMap<>();

        String sql = "SELECT ddv.*, mu.music_url AS musicUrl, 1 as voiceType " +
                "FROM dub_device_voice ddv\n" +
                "LEFT JOIN dub_background_music mu ON mu.id = ddv.background_music_id " +
                "WHERE ddv.device_id = ? and ddv.del_status != 1 " +
                "order by ddv.sortby \n";
        args.add(deviceId);
        List<DeviceVoice> deviceVoices = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DeviceVoice.class),
                args.toArray());

        // 蓝牙语音列表与设备语音列表比对处理
        List<DeviceVoice> dataList = getDeviceVoices(voiceVoList, deviceVoices);

        // 将设备不存在的音频文件，在本地删除
        List<Long> oldDataList = getOldDataList(voiceVoList, deviceVoices);
        if (!oldDataList.isEmpty()) {
            int result = deviceVoiceRepository.update(oldDataList);
            log.info(" delete old voice, reuslt={}, list={}", result, JSON.toJSONString(oldDataList));
        }

        map.put("result", dataList);
        detailVo.setMap(map);
        return ResultBean.successfulResult(detailVo);
    }

    private List<Long> getOldDataList(List<DevVoiceVo> voiceVoList, List<DeviceVoice> deviceVoices) {
        List<Long> oldDataList = new ArrayList<>();
        for (DeviceVoice deviceVoice : deviceVoices) {
            boolean flag = true;
            for (DevVoiceVo voiceVo : voiceVoList) {
                String voiceName = voiceVo.getName().toLowerCase().replace(".mp3", "");
                if (voiceName.equals(deviceVoice.getTitle().toLowerCase())) {
                    flag = false;
                    break;
                }
            }

            if (flag) {
                // 设备不存在的音频文件列表
                oldDataList.add(deviceVoice.getId());
            }
        }
        return oldDataList;
    }

    private List<DeviceVoice> getDeviceVoices(List<DevVoiceVo> voiceVoList, List<DeviceVoice> deviceVoices) {
        List<DeviceVoice> dataList = new ArrayList<>(deviceVoices);
        for (DevVoiceVo voiceVo : voiceVoList) {
            boolean flag = true;
            for (DeviceVoice deviceVoice : deviceVoices) {
                String voiceName = voiceVo.getName().toLowerCase().replace(".mp3", "");
                if (voiceName.equals(deviceVoice.getTitle().toLowerCase())) {
                    flag = false;
                    break;
                }
            }
            if (flag) {
                DeviceVoice deviceVoice = new DeviceVoice();
                deviceVoice.setVoiceName(voiceVo.getName());
                deviceVoice.setTitle(voiceVo.getName());
                deviceVoice.setVoiceType(2);
                dataList.add(deviceVoice);
            }
        }
        return dataList;
    }

    /**
     * 新增设备音频并发送音频到设备
     *
     * @param addParam
     * @return
     */
    @DevicePermission(key = "deviceIds")
    public ResultBean<Boolean> addDeviceVoice(DeviceVoiceAddParam addParam) {
        Integer time = addParam.getTime();
        String url = addParam.getUrl();
        if (StringUtils.isEmpty(url)) {
            return ResultBean.failedResultWithMsg("请重新合成");
        }
        // 生成语音包
        VoicePacket voicePacket = new VoicePacket();
        voicePacket.setShopId(addParam.getShopId());
        voicePacket.setName(addParam.getTitle());
        voicePacket.setVoiceTime(time);
        voicePacket.setFileUrl(url);
        VoicePacket saveVoicePacket = voicePacketRepository.save(voicePacket);

        // 生成作品
        VoiceWork workInfoVo = new VoiceWork();
        workInfoVo.setTitle(addParam.getTitle());
        workInfoVo.setContent(addParam.getContent());
        workInfoVo.setAnchorId(addParam.getVoiceId());
        workInfoVo.setBackgroundMusicId(addParam.getBackgroundMusicId());
        workInfoVo.setBackgroundMusicVolume(addParam.getBackgroundMusicVolume());
        workInfoVo.setPitch(addParam.getPitch());
        workInfoVo.setSpeed(addParam.getSpeed());
        workInfoVo.setVolume(addParam.getVolume());
        workInfoVo.setVoiceTime(time);
        workInfoVo.setShopId(addParam.getShopId());
        workInfoVo.setUserId(addParam.getUserId());
        workInfoVo.setVoiceId(saveVoicePacket.getId());
        workInfoVo.setCreateTime(new Date());
        workInfoVo.setDelStatus(0);
        workInfoVo.setSampleRate(24000);//// 音频采样率
        workRepository.save(workInfoVo);
        voicePacket.setVoiceWorkId(workInfoVo.getId());
        voicePacketRepository.save(voicePacket);
        String finalUrl = url;
        Integer finalTime = time;
        List<DeviceVoice> voiceList = addParam.getDeviceIds().stream().map(devId -> {
            DeviceVoice deviceVoice = new DeviceVoice();
            deviceVoice.setType(addParam.getType());// 设备自带
            deviceVoice.setDeviceId(devId); // 设备id
            deviceVoice.setVoiceWorkId(workInfoVo.getId());// 原作品id
            deviceVoice.setDeviceVoiceId(voicePacket.getId());// 设备音频id = 语音包id
            deviceVoice.setTitle(addParam.getTitle());// 标题
            deviceVoice.setContent(addParam.getContent());// 作品文字内容
            deviceVoice.setVoiceUrl(finalUrl);// 语音包url
            deviceVoice.setVoiceId(addParam.getVoiceId());// 主播id
            deviceVoice.setVoiceTime(finalTime);// 作品时长(秒)
            deviceVoice.setSpeed(addParam.getSpeed());// 语速
            deviceVoice.setVolume(addParam.getVolume());// 音量
            deviceVoice.setPitch(addParam.getPitch());// 语调
            deviceVoice.setBackgroundMusicId(addParam.getBackgroundMusicId());// 背景音乐音量
            deviceVoice.setBackgroundMusicVolume(addParam.getBackgroundMusicVolume());// 背景音乐id
            deviceVoice.setSampleRate(workInfoVo.getSampleRate());//// 音频采样率
            deviceVoice.setDelStatus(0);
            return deviceVoice;
        }).collect(Collectors.toList());
        deviceVoiceRepository.saveAll(voiceList);
        voiceList.forEach(voice -> {
            // 发送音频到设备
            remoteDeviceService.sendAudio(voice.getDeviceId(), cdnUrl + voice.getVoiceUrl(), addParam.getUserId(), voice.getTitle());
        });
        return ResultBean.successfulResult(true);
    }

    public ResultBean<List<DeviceTreeOptionVo>> getTreeUserDeviceSelect(List<Long> shopIds) {
        List<DeviceTreeOptionVo> tree = new ArrayList<>();
        List<DeviceOptionVo> list = getUserDevList(shopIds);
        if (CollUtil.isEmpty(list)) {
            return ResultBean.successfulResult(tree);
        }
        Map<Long, List<DeviceOptionVo>> devMap = list.stream().filter(e -> e.getShopId() != null)
                .collect(Collectors.groupingBy(DeviceOptionVo::getShopId));
        devMap.forEach((k, v) -> {
            DeviceTreeOptionVo parent = new DeviceTreeOptionVo();
            parent.setId(k);
            parent.setType(v.get(0).getType());
            parent.setLabel(v.get(0).getShopName());
            List<DeviceTreeOptionVo> childs = v.stream().map(e -> {
                DeviceTreeOptionVo child = new DeviceTreeOptionVo();
                child.setId(e.getId());
                child.setLabel(e.getName());
                return child;
            }).collect(Collectors.toList());
            parent.setDevice(childs);
            tree.add(parent);
        });
        return ResultBean.successfulResult(tree);
    }

    private List<DeviceOptionVo> getUserDevList(List<Long> shopIds) {
        // 1. 先拼出 ? 占位符
        String inSql = String.join(",", Collections.nCopies(shopIds.size(), "?"));

        String sql = "SELECT dd.*, shop.type, shop.shop_name " +
                "FROM dub_device dd " +
                "LEFT JOIN dub_shop shop ON shop.id = dd.shop_id " +
                "WHERE dd.del_status = 0 AND dd.shop_id IN (" + inSql + ")";
        return jdbcTemplate.query(sql, shopIds.toArray(), new BeanPropertyRowMapper<>(DeviceOptionVo.class));
    }


    public ResultBean uploadDeviceVoice(String accessToken, MultipartFile file, String name) {
        if (file.getSize() > 1024 * 1024 * 10) {
            return ResultBean.failedResultWithMsg("文件最大上传10M");
        }
        Long userId = redisService.findTokenVo(accessToken).getId();
        int index = name.lastIndexOf(".");
        String fileName = name.substring(0, index);
        fileName = FileRenameUtils.filterNonChineseEnglishNumbers(fileName);
        if (fileName.length() > 3) {
            fileName = fileName.substring(0, 3);
        }
        try {
            File processFile = FileUtil.convertMultipartFileToFile(file, appUrl);
            File outFile = new File(appUrl + fileName + ".mp3");
            FileUtil.coverToMp3(processFile, outFile);
            String url = ossService.putFileNew(null, outFile, "temp/" + userId);
            Map<String, Object> map = new HashMap<>(1);
            map.put("url", url);
            map.put("name", fileName);
            outFile.delete();
            return ResultBean.successfulResult(map);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
